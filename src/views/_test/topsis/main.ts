type Types = ('max' | 'min')[]
type Vector = number[]
type Matrix = Vector[]

export function entropyTopsis(matrix: Matrix, types: Types) {
  // 输入验证
  if (!matrix || !Array.isArray(matrix) || matrix.length === 0) {
    throw new Error('矩阵不能为空')
  }

  if (!types || !Array.isArray(types) || types.length !== matrix[0].length) {
    throw new Error('指标类型数组长度必须与矩阵列数相等')
  }

  const m = matrix.length // 方案数
  const n = matrix[0].length // 指标数

  // 步骤1: 数据标准化
  const normalizedMatrix = normalizeMatrix(matrix, types)

  // 步骤2: 计算熵权
  const weights = calculateEntropyWeights(normalizedMatrix)

  // 步骤3: 构建加权决策矩阵
  const weightedMatrix = applyWeights(normalizedMatrix, weights)

  // 步骤4: 确定理想解和负理想解
  const { idealSolution, negativeIdealSolution } = findIdealSolutions(weightedMatrix, types)

  // 步骤5: 计算到理想解和负理想解的距离
  const { positiveDistances, negativeDistances } = calculateDistances(
    weightedMatrix,
    idealSolution,
    negativeIdealSolution,
  )

  // 步骤6: 计算TOPSIS得分
  const scores = calculateTopsisScores(positiveDistances, negativeDistances)

  // 步骤7: 排名
  const ranking = getRanking(scores)

  return {
    normalizedMatrix,
    weights,
    weightedMatrix,
    idealSolution,
    negativeIdealSolution,
    positiveDistances,
    negativeDistances,
    scores,
    ranking,
  }
}

/**
 * 数据标准化（正向化处理）
 */
function normalizeMatrix(matrix: Matrix, types: Types) {
  const m = matrix.length
  const n = matrix[0].length
  const normalized = Array(m)
    .fill()
    .map(() => Array(n).fill(0))

  for (let j = 0; j < n; j++) {
    const column = matrix.map((row) => row[j])
    const max = Math.max(...column)
    const min = Math.min(...column)
    const range = max - min

    for (let i = 0; i < m; i++) {
      if (types[j] === 'max') {
        // 效益型指标：越大越好
        normalized[i][j] = range === 0 ? 1 : (matrix[i][j] - min) / range
      } else {
        // 成本型指标：越小越好
        normalized[i][j] = range === 0 ? 1 : (max - matrix[i][j]) / range
      }
    }
  }

  return normalized
}

/**
 * 计算熵权
 */
function calculateEntropyWeights(matrix: Matrix) {
  const m = matrix.length
  const n = matrix[0].length
  const weights = Array(n).fill(0)
  const k = 1 / Math.log(m) // 熵的系数

  for (let j = 0; j < n; j++) {
    // 计算每个指标的熵值
    let entropy = 0
    const columnSum = matrix.reduce((sum, row) => sum + row[j], 0)

    for (let i = 0; i < m; i++) {
      const pij = columnSum === 0 ? 0 : matrix[i][j] / columnSum
      if (pij > 0) {
        entropy -= pij * Math.log(pij)
      }
    }
    entropy *= k

    // 计算熵权
    weights[j] = 1 - entropy
  }

  // 权重归一化
  const totalWeight = weights.reduce((sum, w) => sum + w, 0)
  return weights.map((w) => (totalWeight === 0 ? 1 / n : w / totalWeight))
}

/**
 * 应用权重到标准化矩阵
 */
function applyWeights(matrix: Matrix, weights: Vector) {
  return matrix.map((row) => row.map((value, j) => value * weights[j]))
}

/**
 * 确定理想解和负理想解
 */
function findIdealSolutions(weightedMatrix: Matrix, types: Types) {
  const n = weightedMatrix[0].length
  const idealSolution = Array(n).fill(0)
  const negativeIdealSolution = Array(n).fill(0)

  for (let j = 0; j < n; j++) {
    const column = weightedMatrix.map((row) => row[j])
    const max = Math.max(...column)
    const min = Math.min(...column)

    if (types[j] === 'max') {
      idealSolution[j] = max
      negativeIdealSolution[j] = min
    } else {
      idealSolution[j] = min
      negativeIdealSolution[j] = max
    }
  }

  return { idealSolution, negativeIdealSolution }
}

/**
 * 计算到理想解和负理想解的距离
 */
function calculateDistances(
  weightedMatrix: Matrix,
  idealSolution: Vector,
  negativeIdealSolution: Vector,
) {
  const m = weightedMatrix.length
  const positiveDistances = Array(m).fill(0)
  const negativeDistances = Array(m).fill(0)

  for (let i = 0; i < m; i++) {
    let posSum = 0
    let negSum = 0

    for (let j = 0; j < weightedMatrix[i].length; j++) {
      posSum += Math.pow(weightedMatrix[i][j] - idealSolution[j], 2)
      negSum += Math.pow(weightedMatrix[i][j] - negativeIdealSolution[j], 2)
    }

    positiveDistances[i] = Math.sqrt(posSum)
    negativeDistances[i] = Math.sqrt(negSum)
  }

  return { positiveDistances, negativeDistances }
}

/**
 * 计算TOPSIS得分
 */
function calculateTopsisScores(positiveDistances: Vector, negativeDistances: Vector) {
  return positiveDistances.map((pos, i) => {
    const neg = negativeDistances[i]
    return pos + neg === 0 ? 0 : neg / (pos + neg)
  })
}

/**
 * 获取排名
 */
function getRanking(scores: Vector) {
  return scores
    .map((score, index) => ({ index, score }))
    .sort((a, b) => b.score - a.score)
    .map((item, rank) => ({
      originalIndex: item.index,
      score: item.score,
      rank: rank + 1,
    }))
}
