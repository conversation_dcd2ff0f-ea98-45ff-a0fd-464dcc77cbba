type CriteriaType = 'max' | 'min'
type Matrix = number[][]
type Vector = number[]

interface RankingItem {
  index: number
  score: number
}

interface IdealSolutions {
  idealSolution: Vector
  negativeIdealSolution: Vector
}

interface Distances {
  distancesToIdeal: Vector
  distancesToNegativeIdeal: Vector
}

interface InternalResults {
  weights: Vector
  scores: Vector
  ranking: number[]
  originalMatrix: Matrix
  normalizedMatrix: Matrix
  weightedMatrix: Matrix
  idealSolution: Vector
  negativeIdealSolution: Vector
  distancesToIdeal: Vector
  distancesToNegativeIdeal: Vector
}

interface SimpleTopsisResult {
  normalizedMatrix: Matrix
  weights: Vector
  weightedMatrix: Matrix
  idealSolution: Vector
  negativeIdealSolution: Vector
  positiveDistances: Vector
  negativeDistances: Vector
  scores: Vector
  ranking: Array<{
    originalIndex: number
    score: number
    rank: number
  }>
}

/**
 * 熵权TOPSIS多属性决策分析方法
 */
class EntropyTOPSIS {
  private originalMatrix: Matrix
  private criteriaTypes: CriteriaType[]
  private normalizedMatrix: Matrix | null = null
  private weights: Vector | null = null
  private weightedMatrix: Matrix | null = null
  private idealSolution: Vector | null = null
  private negativeIdealSolution: Vector | null = null
  private distancesToIdeal: Vector | null = null
  private distancesToNegativeIdeal: Vector | null = null
  private scores: Vector | null = null
  private ranking: RankingItem[] | null = null

  constructor(matrix: Matrix, criteriaTypes: CriteriaType[]) {
    this.originalMatrix = matrix.map((row) => [...row])
    this.criteriaTypes = criteriaTypes

    this.validateInput()
  }

  /**
   * 验证输入数据
   */
  private validateInput(): void {
    if (!this.originalMatrix || this.originalMatrix.length === 0) {
      throw new Error('决策矩阵不能为空')
    }

    const cols = this.originalMatrix[0].length
    if (this.originalMatrix.some((row) => row.length !== cols)) {
      throw new Error('决策矩阵必须是矩形矩阵')
    }

    if (this.criteriaTypes.length !== cols) {
      throw new Error('指标类型数组长度必须与矩阵列数相等')
    }
  }

  /**
   * 数据正向化处理
   * 将成本型指标转换为效益型指标
   */
  private positiveOrientation(): Matrix {
    const matrix = this.originalMatrix.map((row) => [...row])

    for (let j = 0; j < this.criteriaTypes.length; j++) {
      if (this.criteriaTypes[j] === 'min') {
        // 成本型指标：最大值减去当前值
        const maxVal = Math.max(...matrix.map((row) => row[j]))
        for (let i = 0; i < matrix.length; i++) {
          matrix[i][j] = maxVal - matrix[i][j]
        }
      }
    }

    return matrix
  }

  /**
   * 数据标准化（归一化）
   * 使用向量归一化方法
   */
  private normalize(matrix: Matrix): Matrix {
    const normalizedMatrix = matrix.map((row) => [...row])

    for (let j = 0; j < matrix[0].length; j++) {
      const sumSquares = matrix.reduce((sum, row) => sum + row[j] * row[j], 0)
      const norm = Math.sqrt(sumSquares)

      // 处理范数为0的情况
      if (norm === 0 || !isFinite(norm)) {
        console.warn(`指标${j + 1}的范数为0或无穷大，该列保持原值`)
        continue
      }

      // 归一化
      for (let i = 0; i < matrix.length; i++) {
        const normalizedValue = matrix[i][j] / norm
        normalizedMatrix[i][j] = isFinite(normalizedValue) ? normalizedValue : 0
      }
    }

    return normalizedMatrix
  }

  /**
   * 计算熵权
   */
  private calculateEntropyWeights(normalizedMatrix: Matrix): Vector {
    const m = normalizedMatrix.length
    const n = normalizedMatrix[0].length
    const weights: number[] = []

    if (m <= 1) {
      console.warn('方案数量不足，无法计算熵权，使用等权重')
      return new Array(n).fill(1 / n)
    }

    const logM = Math.log(m)
    if (logM === 0) {
      console.warn('方案数量导致对数为0，使用等权重')
      return new Array(n).fill(1 / n)
    }

    for (let j = 0; j < n; j++) {
      const columnSum = normalizedMatrix.reduce((sum, row) => sum + row[j], 0)

      if (columnSum === 0 || !isFinite(columnSum)) {
        console.warn(`指标${j + 1}的列和为0或无穷大，该指标权重设为0`)
        weights.push(0)
        continue
      }

      let entropy = 0

      for (let i = 0; i < m; i++) {
        const pij = normalizedMatrix[i][j] / columnSum

        if (pij > 0 && isFinite(pij) && pij <= 1) {
          const logPij = Math.log(pij)
          if (isFinite(logPij)) {
            entropy -= pij * logPij
          }
        }
      }

      // 标准化熵值
      entropy = entropy / logM

      const gj = 1 - entropy

      const weight = Math.max(gj, 0)
      weights.push(isFinite(weight) ? weight : 0)
    }

    // 归一化权重
    const sumWeights = weights.reduce((sum, w) => sum + w, 0)

    if (sumWeights === 0 || !isFinite(sumWeights)) {
      console.warn('所有指标权重为0或无效，使用等权重')
      return new Array(n).fill(1 / n)
    }

    return weights.map((w) => w / sumWeights)
  }

  /**
   * 构建加权标准化决策矩阵
   */
  private buildWeightedMatrix(normalizedMatrix: Matrix, weights: Vector): Matrix {
    return normalizedMatrix.map((row) => row.map((val, j) => val * weights[j]))
  }

  /**
   * 确定正理想解和负理想解
   */
  private findIdealSolutions(weightedMatrix: Matrix): IdealSolutions {
    const n = weightedMatrix[0].length
    const idealSolution: number[] = []
    const negativeIdealSolution: number[] = []

    for (let j = 0; j < n; j++) {
      const column = weightedMatrix.map((row) => row[j])
      idealSolution.push(Math.max(...column))
      negativeIdealSolution.push(Math.min(...column))
    }

    return { idealSolution, negativeIdealSolution }
  }

  /**
   * 计算欧式距离
   */
  private calculateDistance(vector1: Vector, vector2: Vector): number {
    return Math.sqrt(vector1.reduce((sum, val, i) => sum + Math.pow(val - vector2[i], 2), 0))
  }

  /**
   * 计算各方案到理想解的距离
   */
  private calculateDistances(
    weightedMatrix: Matrix,
    idealSolution: Vector,
    negativeIdealSolution: Vector,
  ): Distances {
    const distancesToIdeal: number[] = []
    const distancesToNegativeIdeal: number[] = []

    weightedMatrix.forEach((row) => {
      distancesToIdeal.push(this.calculateDistance(row, idealSolution))
      distancesToNegativeIdeal.push(this.calculateDistance(row, negativeIdealSolution))
    })

    return { distancesToIdeal, distancesToNegativeIdeal }
  }

  /**
   * 计算相对贴近度
   */
  private calculateScores(distancesToIdeal: Vector, distancesToNegativeIdeal: Vector): Vector {
    return distancesToIdeal.map((dIdeal, i) => {
      const dNegative = distancesToNegativeIdeal[i]
      const denominator = dIdeal + dNegative

      // 处理分母为0的情况
      if (denominator === 0 || !isFinite(denominator)) {
        console.warn(`方案${i + 1}的距离计算异常，设置得分为0.5`)
        return 0.5
      }

      if (!isFinite(dNegative)) {
        console.warn(`方案${i + 1}到负理想解的距离异常，设置得分为0`)
        return 0
      }

      const score = dNegative / denominator

      if (!isFinite(score)) {
        console.warn(`方案${i + 1}的得分计算异常，设置得分为0`)
        return 0
      }

      return Math.max(0, Math.min(1, score))
    })
  }

  /**
   * 执行完整的熵权TOPSIS分析
   */
  public analyze(): InternalResults {
    const positiveMatrix = this.positiveOrientation()
    this.normalizedMatrix = this.normalize(positiveMatrix)

    this.weights = this.calculateEntropyWeights(this.normalizedMatrix)

    this.weightedMatrix = this.buildWeightedMatrix(this.normalizedMatrix, this.weights)

    const solutions = this.findIdealSolutions(this.weightedMatrix)
    this.idealSolution = solutions.idealSolution
    this.negativeIdealSolution = solutions.negativeIdealSolution

    const distances = this.calculateDistances(
      this.weightedMatrix,
      this.idealSolution,
      this.negativeIdealSolution,
    )
    this.distancesToIdeal = distances.distancesToIdeal
    this.distancesToNegativeIdeal = distances.distancesToNegativeIdeal

    this.scores = this.calculateScores(this.distancesToIdeal, this.distancesToNegativeIdeal)

    this.ranking = this.scores
      .map((score, index) => ({ index, score }))
      .sort((a, b) => b.score - a.score)

    return this.getResults()
  }

  /**
   * 获取分析结果
   */
  public getResults(): InternalResults {
    if (
      !this.weights ||
      !this.scores ||
      !this.ranking ||
      !this.normalizedMatrix ||
      !this.weightedMatrix ||
      !this.idealSolution ||
      !this.negativeIdealSolution ||
      !this.distancesToIdeal ||
      !this.distancesToNegativeIdeal
    ) {
      throw new Error('分析尚未完成，请先调用 analyze() 方法')
    }

    return {
      weights: this.weights,
      scores: this.scores,
      ranking: this.ranking.map((item) => item.index),

      originalMatrix: this.originalMatrix,
      normalizedMatrix: this.normalizedMatrix,
      weightedMatrix: this.weightedMatrix,
      idealSolution: this.idealSolution,
      negativeIdealSolution: this.negativeIdealSolution,
      distancesToIdeal: this.distancesToIdeal,
      distancesToNegativeIdeal: this.distancesToNegativeIdeal,
    }
  }
}

/**
 * 熵权TOPSIS分析的简化函数接口
 * @param matrix - 决策矩阵
 * @param types - 指标类型数组
 * @returns 分析结果
 */
export function entropyTopsis(matrix: Matrix, types: CriteriaType[]): SimpleTopsisResult {
  const topsis = new EntropyTOPSIS(matrix, types)
  const results = topsis.analyze()

  return {
    normalizedMatrix: results.normalizedMatrix,
    weights: results.weights,
    weightedMatrix: results.weightedMatrix,
    idealSolution: results.idealSolution,
    negativeIdealSolution: results.negativeIdealSolution,
    positiveDistances: results.distancesToIdeal,
    negativeDistances: results.distancesToNegativeIdeal,
    scores: results.scores,
    ranking: results.ranking.map((index, rank) => ({
      originalIndex: index,
      score: results.scores[index],
      rank: rank + 1,
    })),
  }
}
