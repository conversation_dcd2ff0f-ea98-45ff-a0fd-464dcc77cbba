<template></template>
<script setup lang="ts">
import { entropyTopsis } from './main'

const matrix = [
  // 指标: C1(成本), C2(效益), C3(效益), C4(效益)
  [0.92, 5.5, 1, 0.75], // 供应商A
  [0.85, 6, 2.52, 0.54], // 供应商B
  [0.87, 7.6, 4.6, 0.7], // 供应商C
  [0.9, 8.2, 5.42, 0.82], // 供应商D
  [0.7, 7.1, 2.2, 0.6], // 供应商D
  [0.78, 4.2, 1.8, 0.45], // 供应商D
]

const criteriaTypes = ['max', 'max', 'max', 'max']
const alternativeNames = ['A', 'B', 'C', 'D', 'E', 'F']
const criteriaNames = ['指标1', '指标2', '指标3', '指标4']
const result = entropyTopsis(matrix, criteriaTypes as any)
debugger
</script>
