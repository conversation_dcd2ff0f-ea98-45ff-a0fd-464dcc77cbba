<template>
  <div class="topsis-container">
    <div class="header">
      <h2>熵权TOPSIS多属性决策分析</h2>
      <p class="description">基于信息熵的TOPSIS方法进行方案评价与排序</p>
    </div>

    <!-- 输入区域 -->
    <div class="input-section">
      <h3>输入数据</h3>

      <!-- 方案和指标设置 -->
      <div class="config-row">
        <div class="config-item">
          <label>方案数量:</label>
          <el-input-number
            v-model="schemeCount"
            :min="2"
            :max="10"
            @change="initializeData"
          />
        </div>
        <div class="config-item">
          <label>指标数量:</label>
          <el-input-number
            v-model="criteriaCount"
            :min="2"
            :max="8"
            @change="initializeData"
          />
        </div>
        <div class="config-item">
          <el-button
            type="primary"
            @click="loadExample"
            >加载示例数据</el-button
          >
          <el-button @click="calculate">计算分析</el-button>
        </div>
      </div>

      <!-- 指标类型设置 -->
      <div class="criteria-types">
        <h4>指标类型设置</h4>
        <div class="type-row">
          <div
            v-for="(_, index) in criteriaTypes"
            :key="index"
            class="type-item"
          >
            <label>指标{{ index + 1 }}:</label>
            <el-select v-model="criteriaTypes[index]">
              <el-option
                label="效益型(越大越好)"
                value="max"
              />
              <el-option
                label="成本型(越小越好)"
                value="min"
              />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 决策矩阵输入 -->
      <div class="matrix-input">
        <h4>决策矩阵</h4>
        <table class="matrix-table">
          <thead>
            <tr>
              <th>方案</th>
              <th
                v-for="j in criteriaCount"
                :key="j"
              >
                指标{{ j }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="i in schemeCount"
              :key="i"
            >
              <td>方案{{ i }}</td>
              <td
                v-for="j in criteriaCount"
                :key="j"
              >
                <el-input-number
                  v-model="decisionMatrix[i - 1][j - 1]"
                  :precision="2"
                  size="small"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 结果展示区域 -->
    <div
      v-if="results"
      class="results-section"
    >
      <h3>分析结果</h3>

      <!-- 权重结果 -->
      <div class="result-item">
        <h4>熵权权重</h4>
        <div class="weights-display">
          <div
            v-for="(weight, index) in results.weights"
            :key="index"
            class="weight-item"
          >
            <span>指标{{ index + 1 }}: {{ (weight * 100).toFixed(2) }}%</span>
          </div>
        </div>
      </div>

      <!-- 最终排名 -->
      <div class="result-item">
        <h4>方案排名</h4>
        <table class="ranking-table">
          <thead>
            <tr>
              <th>排名</th>
              <th>方案</th>
              <th>TOPSIS得分</th>
              <th>相对接近度</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in results.ranking"
              :key="item.originalIndex"
            >
              <td>{{ item.rank }}</td>
              <td>方案{{ item.originalIndex + 1 }}</td>
              <td>{{ item.score.toFixed(4) }}</td>
              <td>{{ (item.score * 100).toFixed(2) }}%</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 详细计算过程 -->
      <el-collapse
        v-model="activeCollapse"
        class="detail-collapse"
      >
        <el-collapse-item
          title="查看详细计算过程"
          name="details"
        >
          <!-- 标准化矩阵 -->
          <div class="detail-section">
            <h5>标准化矩阵</h5>
            <table class="detail-table">
              <thead>
                <tr>
                  <th>方案</th>
                  <th
                    v-for="j in criteriaCount"
                    :key="j"
                  >
                    指标{{ j }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(row, i) in results.normalizedMatrix"
                  :key="i"
                >
                  <td>方案{{ i + 1 }}</td>
                  <td
                    v-for="(value, j) in row"
                    :key="j"
                  >
                    {{ value.toFixed(4) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 加权决策矩阵 -->
          <div class="detail-section">
            <h5>加权决策矩阵</h5>
            <table class="detail-table">
              <thead>
                <tr>
                  <th>方案</th>
                  <th
                    v-for="j in criteriaCount"
                    :key="j"
                  >
                    指标{{ j }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(row, i) in results.weightedMatrix"
                  :key="i"
                >
                  <td>方案{{ i + 1 }}</td>
                  <td
                    v-for="(value, j) in row"
                    :key="j"
                  >
                    {{ value.toFixed(4) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 理想解 -->
          <div class="detail-section">
            <h5>理想解与负理想解</h5>
            <table class="detail-table">
              <thead>
                <tr>
                  <th>解类型</th>
                  <th
                    v-for="j in criteriaCount"
                    :key="j"
                  >
                    指标{{ j }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>正理想解</td>
                  <td
                    v-for="(value, j) in results.idealSolution"
                    :key="j"
                  >
                    {{ value.toFixed(4) }}
                  </td>
                </tr>
                <tr>
                  <td>负理想解</td>
                  <td
                    v-for="(value, j) in results.negativeIdealSolution"
                    :key="j"
                  >
                    {{ value.toFixed(4) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 距离计算 -->
          <div class="detail-section">
            <h5>距离计算</h5>
            <table class="detail-table">
              <thead>
                <tr>
                  <th>方案</th>
                  <th>到正理想解距离</th>
                  <th>到负理想解距离</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(_, i) in results.positiveDistances"
                  :key="i"
                >
                  <td>方案{{ i + 1 }}</td>
                  <td>{{ results.positiveDistances[i].toFixed(4) }}</td>
                  <td>{{ results.negativeDistances[i].toFixed(4) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { entropyTopsis } from './main'

// 响应式数据
const schemeCount = ref(4) // 方案数量
const criteriaCount = ref(3) // 指标数量
const criteriaTypes = ref<('max' | 'min')[]>(['max', 'max', 'min']) // 指标类型
const decisionMatrix = ref<number[][]>([]) // 决策矩阵
const results = ref<any>(null) // 计算结果
const activeCollapse = ref<string[]>([]) // 折叠面板状态

// 初始化数据
const initializeData = () => {
  // 初始化指标类型数组
  criteriaTypes.value = Array(criteriaCount.value).fill('max')

  // 初始化决策矩阵
  decisionMatrix.value = Array(schemeCount.value)
    .fill(null)
    .map(() => Array(criteriaCount.value).fill(0))

  // 清空结果
  results.value = null
}

// 加载示例数据
const loadExample = () => {

  // 示例：选择供应商问题
  // 指标：质量评分(效益型)、价格(成本型)、交货时间(成本型)、服务评分(效益型)
  schemeCount.value = 6
  criteriaCount.value = 4

  nextTick(() => {
    criteriaTypes.value = ['max', 'min', 'min', 'max'] // 质量↑, 价格↓, 交货时间↓, 服务↑

    decisionMatrix.value = [
      [0.92, 5.5, 1, 0.75], // 供应商A
      [0.85, 6, 2.52, 0.54], // 供应商B
      [0.87, 7.6, 4.6, 0.7], // 供应商C
      [0.9, 8.2, 5.42, 0.82], // 供应商D
      [0.7, 7.1, 2.2, 0.6], // 供应商D
      [0.78, 4.2, 1.8, 0.45], // 供应商D
    ]

    ElMessage.success('已加载供应商选择示例数据')
  })
}

// 计算TOPSIS分析
const calculate = () => {
  try {
    // 验证输入数据
    if (!decisionMatrix.value || decisionMatrix.value.length === 0) {
      ElMessage.error('请先输入决策矩阵数据')
      return
    }

    // 检查是否有空值
    for (let i = 0; i < decisionMatrix.value.length; i++) {
      for (let j = 0; j < decisionMatrix.value[i].length; j++) {
        if (decisionMatrix.value[i][j] === null || decisionMatrix.value[i][j] === undefined) {
          ElMessage.error(`方案${i + 1}的指标${j + 1}数据不能为空`)
          return
        }
      }
    }
debugger
    // 调用熵权TOPSIS算法
    results.value = entropyTopsis(decisionMatrix.value, criteriaTypes.value)

    ElMessage.success('计算完成！')
  } catch (error: any) {
    ElMessage.error(`计算错误: ${error.message}`)
    console.error('TOPSIS计算错误:', error)
  }
}

// 监听方案数和指标数变化，自动初始化数据
watch(
  [schemeCount, criteriaCount],
  () => {
    debugger
    initializeData()
  },
  { immediate: true },
)
</script>
