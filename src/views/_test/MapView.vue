<template>
  <div class="map-container">
    <z-dynamic-route
      :route="{
        name: '添加应用',
        path: 'add',
        viewPath: '@/views/Screen/Screen.vue',
        meta: {
          title: '添加应用',
          layout: 'blank',
        },
      }"
      :inherit="false"
      :persistent="true"
    >
      <template #default="{ navigate, route }">
        <el-button
          @click="
            () =>
              navigate({
                query: {
                  mode: 'create',
                },
              })
          "
        >
          添加应用 {{ route }}
        </el-button>
      </template>
    </z-dynamic-route>

    <z-dynamic-route
      :route="{
        name: '熵权TOPSIS1',
        path: 'topsis',
        viewPath: '@/views/_test/topsis/index.vue',
        meta: {
          title: '熵权TOPSIS',
          layout: 'blank',
        },
      }"
    >
      <template #default="{ navigate }">
        <el-button
          @click="
            () =>
              navigate({
                query: {
                  mode: 'create',
                },
              })
          "
        >
          熵权TOPSIS
        </el-button>
      </template>
    </z-dynamic-route>
    <el-button @click="drillUp">drillUp</el-button>

    <!-- 添加下钻功能按钮组 -->
    <el-button-group class="drill-controls">
      <el-button
        @click="showDrillByNameDialog"
        :icon="Location"
      >
        按名称下钻
      </el-button>
      <el-button
        @click="showDrillByGBDialog"
        :icon="Position"
      >
        按GB编码下钻
      </el-button>
    </el-button-group>

    <TMapView
      ref="mapRef"
      :options="mapOptions"
      @ready="onMapReady"
      @error="onMapError"
      @select-result="onSelectResult"
    />
    <div class="map-controls">
      <el-button-group>
        <el-button
          @click="handleZoomIn"
          :icon="ZoomIn"
          >放大
        </el-button>
        <el-button
          @click="handleZoomOut"
          :icon="ZoomOut"
          >缩小
        </el-button>
        <el-button
          @click="handleResetView"
          :icon="House"
          >复位
        </el-button>
        <el-button
          @click="toggleOverviewMap"
          :icon="Picture"
        >
          {{ overviewMapVisible ? '隐藏鹰眼' : '显示鹰眼' }}
        </el-button>
        <el-button
          @click="handleGetZoom"
          :icon="Search"
          >获取缩放级别
        </el-button>
      </el-button-group>

      <el-button-group class="mark-controls">
        <el-button
          @click="toggleMarkTool"
          :type="isMarkToolActive ? 'primary' : ''"
          :icon="Location"
        >
          {{ isMarkToolActive ? '停止标注' : '开始标注' }}
        </el-button>
        <el-button
          @click="clearMarks"
          :icon="Delete"
          :disabled="!hasMarkers"
        >
          清除标注
        </el-button>
      </el-button-group>

      <el-button-group class="layer-controls">
        <el-button
          @click="switchBaseLayer('vec')"
          :type="currentLayer === 'vec' ? 'primary' : ''"
        >
          矢量图
        </el-button>
        <el-button
          @click="switchBaseLayer('img')"
          :type="currentLayer === 'img' ? 'primary' : ''"
        >
          影像图
        </el-button>
        <el-button
          @click="switchBaseLayer('ter')"
          :type="currentLayer === 'ter' ? 'primary' : ''"
        >
          地形图
        </el-button>
      </el-button-group>

      <el-button-group class="geocoder-controls">
        <el-button
          @click="handleGeocodeTest"
          :icon="Location"
        >
          地址解析测试
        </el-button>
        <el-button
          @click="handleReverseGeocodeTest"
          :icon="Position"
        >
          逆地址解析
        </el-button>
      </el-button-group>

      <!-- 绘制工具按钮组 -->
      <el-button-group class="draw-controls">
        <el-button
          @click="togglePolygonTool"
          :type="polygonToolActive ? 'primary' : ''"
          :icon="Edit"
        >
          {{ polygonToolActive ? '停止绘制多边形' : '绘制多边形' }}
        </el-button>
        <el-button
          @click="togglePolylineTool"
          :type="polylineToolActive ? 'primary' : ''"
          :icon="Connection"
        >
          {{ polylineToolActive ? '停止绘制折线' : '绘制折线' }}
        </el-button>
      </el-button-group>

      <!-- 添加图层测试按钮组 -->
      <el-button-group class="layer-test-controls">
        <el-button
          @click="addToDefaultLayer"
          :icon="Plus"
        >
          默认图层添加标记
        </el-button>
        <el-button
          @click="addToCustomLayer"
          :icon="Plus"
        >
          自定义图层添加标记
        </el-button>
        <el-button
          @click="clearDefaultLayer"
          :icon="Delete"
        >
          清除默认图层
        </el-button>
        <el-button
          @click="removeCustomLayer"
          :icon="Delete"
        >
          移除自定义图层
        </el-button>
      </el-button-group>

      <el-button-group class="info-window-controls">
        <el-button
          @click="showInfoWindow"
          :icon="ChatLineRound"
        >
          显示信息窗口
        </el-button>
        <el-button
          @click="showComponentInInfoWindow"
          :icon="View"
        >
          显示组件窗口
        </el-button>
      </el-button-group>

      <!-- TFeatureLayer 测试按钮组 -->
      <el-button-group class="feature-layer-controls">
        <el-button
          @click="testFeatureLayerPolygons"
          :icon="Edit"
        >
          测试多边形要素
        </el-button>
        <el-button
          @click="testFeatureLayerPoints"
          :icon="Location"
        >
          测试点要素
        </el-button>
        <el-button
          @click="testFeatureLayerLines"
          :icon="Connection"
        >
          测试线要素
        </el-button>
        <el-button
          @click="testFeatureLayerRivers"
          :icon="View"
        >
          测试河流面要素
        </el-button>
      </el-button-group>

      <!-- TFeatureLayer 高级测试按钮组 -->
      <el-button-group class="feature-layer-advanced-controls">
        <el-button
          @click="testFeatureLayerMixed"
          :icon="Connection"
        >
          测试混合要素
        </el-button>
        <el-button
          @click="testFeatureLayerWKT"
          :icon="DataAnalysis"
        >
          测试WKT数据
        </el-button>
        <el-button
          @click="clearFeatureLayer"
          :icon="Delete"
          :disabled="!featureLayer"
        >
          清除要素图层
        </el-button>
      </el-button-group>
    </div>

    <el-dialog
      v-model="geocodeDialogVisible"
      title="地址解析测试"
      width="30%"
    >
      <el-input
        v-model="addressInput"
        placeholder="请输入要解析的地址"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="geocodeDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="performGeocode"
          >
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加按名称下钻对话框 -->
    <el-dialog
      v-model="drillByNameDialogVisible"
      title="按名称下钻"
      width="30%"
    >
      <el-input
        v-model="drillNameInput"
        placeholder="请输入区域名称"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="drillByNameDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="performDrillByName"
          >
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加按GB编码下钻对话框 -->
    <el-dialog
      v-model="drillByGBDialogVisible"
      title="按GB编码下钻"
      width="30%"
    >
      <el-input
        v-model="drillGBInput"
        placeholder="请输入GB编码"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="drillByGBDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="performDrillByGB"
          >
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, markRaw, onMounted } from 'vue'
import {
  TMapView,
  TMap,
  TSearchType,
  TLabel,
  TSvgOverlay,
  TPointMarkerMode,
  THtmlOverlay,
  TComponentOverlay,
  TVNodeOverlay,
  TPolygonTool,
  TPolylineTool,
  TPolyline,
  TPolygon,
  TMarker,
  TInfoWindow,
  TAnchorPosition,
  TAnnotationMarker,
  TFeatureLayer,
} from '@/map'
import { parse as parseWKT } from 'terraformer-wkt-parser'
import MyCustomComponent from './MyCustomComponent.vue'
import {
  Location,
  ZoomIn,
  ZoomOut,
  House,
  Picture,
  Search,
  Delete,
  Position,
  Edit,
  Connection,
  Plus,
  ChatLineRound,
  View,
  DataAnalysis,
} from '@element-plus/icons-vue'
import { ElButton, ElMessage } from 'element-plus'
import type { TLayerType, TNativeOverviewMap, TPointMarkerTool } from '@/map'
import type { TOverlayLayer } from '@/map/core/layer/TOverlayLayer'
import { TGeoJSONLayer } from '@/map/core/layer/TGeoJSONLayer'
import geojsonData from '@/map/assets/geo/baoji.json'
import { metersToPixels } from '@/map/utils/map'
import dataResApi from '@/api/dataResources.ts'

const mapRef = ref<InstanceType<typeof TMapView> | null>(null)
const overviewMapVisible = ref(false)
let overviewMapInstance: TNativeOverviewMap | null = null
const markTool = ref<TPointMarkerTool | null>(null)
const markToolActive = ref(false)
const markersCount = ref(0)
const geocodeDialogVisible = ref(false)
const addressInput = ref('')

const polygonTool = ref<TPolygonTool | null>(null)
const polylineTool = ref<TPolylineTool | null>(null)
const polygonToolActive = ref(false)
const polylineToolActive = ref(false)
const thematicRemoteList = ref([])

onMounted(() => {
  dataResApi
    .getRemoteDataList({})
    .then((res) => {
      thematicRemoteList.value = res.data.data.map((item) => {
        const geometry = parseWKT(item.geom)
        return {
          type: 'Feature',
          properties: item,
          geometry,
        }
      })
      console.log('WKT 数据加载完成，数量:', thematicRemoteList.value.length)
    })
    .catch((error) => {
      console.error('加载 WKT 数据失败:', error)
    })
})

// 在操作标注工具时更新状态
const toggleMarkTool = () => {
  if (markTool.value) {
    const result = markTool.value.toggle()
    markToolActive.value = result
    if (result) {
      ElMessage.info('请点击地图添加标注')
    }
  }
}
// 监听标注工具状态
const updateMarkToolState = () => {
  markToolActive.value = markTool.value?.isActivated() ?? false
}

// 使用计算属性来访问状态
const isMarkToolActive = computed(() => markToolActive.value)

// 当前底图类型
const currentLayer = ref<TLayerType>('vec')

// 切换鹰眼地图显示状态
const toggleOverviewMap = () => {
  if (!mapRef.value) return

  const map = mapRef.value.getMap()
  if (!map) return

  if (!overviewMapInstance) {
    // 首次创建鹰眼地图
    overviewMapInstance = map.addOverviewMap({
      isOpen: true,
      size: new T.Point(150, 150),
    })

    // 监听视图变化事件
    overviewMapInstance.addEventListener('viewchange', (e) => {
      overviewMapVisible.value = e.isOpen
      console.log('鹰眼状态变化:', e.isOpen)
    })

    overviewMapVisible.value = true
  } else {
    // 切换显示/隐藏状态
    overviewMapInstance.changeView()
  }
}

// 地图控制方法
const handleZoomIn = () => {
  mapRef.value?.zoomIn()
}

const handleZoomOut = () => {
  mapRef.value?.zoomOut()
}

const handleResetView = () => {
  mapRef.value?.resetView()
}

const onSelectResult = (result: any) => {
  const [lng, lat] = result.lonlat.split(',').map(Number)
  markTool.value?.addMarkerAt([lng, lat])
}

// 切换底图
const switchBaseLayer = (type: TLayerType) => {
  if (!mapRef.value) return

  const map = mapRef.value.getMap()
  if (!map) return

  map.switchBaseLayer(type)
  currentLayer.value = type
}

const mapOptions = {
  center: {
    lng: 107.237743,
    lat: 34.363184,
  },
  zoom: 8.5,
  minZoom: 3,
  maxZoom: 18,
  search: {
    show: true,
    type: TSearchType.VIEWPORT,
    placeholder: '搜索地点或地址',
    timeout: 15000,
    maxResults: 20,
  },
}

const testPoints = [
  { lng: 105.99609, lat: 34.20272, name: '测试点1' },
  { lng: 105.7489, lat: 34.71452, name: '测试点2' },
  { lng: 107.257743, lat: 34.383184, name: '测试点3' },
  { lng: 116.407428, lat: 39.90923, name: '测试点4' },
  { lng: 116.407428, lat: 39.91923, name: '测试点5' },
]

// 1. Marker 测试
const testMarkers = () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  // // 添加普通标记
  // map.addOverlay(
  //   new TMarker({
  //     position: [testPoints[0].lng, testPoints[0].lat],
  //     title: '基础标记',

  //     icon: 'https://api.tianditu.gov.cn/img/map/markerA.png',
  //     // icon: MARKER_ICONS.A.url,
  //     width: 32,
  //     height: 32,
  //     markerStyle: {
  //       fill: 'red',
  //     },
  //     events: {
  //       click: (e) => {
  //         console.log(e)
  //         ElMessage.info('点击了基础标记')
  //       },
  //     },
  //   }),
  // )

  // // 添加可拖拽标记
  // map.addOverlay(
  //   new TMarker({
  //     position: [testPoints[1].lng, testPoints[1].lat],
  //     title: '可拖拽标记',
  //     draggable: true,
  //     icon: 'https://api.tianditu.gov.cn/img/map/markerA.png',
  //     markerStyle: {
  //       cursor: 'move',
  //       border: '2px solid #ff4757',
  //       borderRadius: '50%',
  //     },
  //   }),
  // )

  // 添加带自定义样式的标记
  map.addOverlay(
    new TMarker({
      labelRemoveable: true,
      position: [testPoints[2].lng, testPoints[2].lat],
      label: '自定义样式标记',
      icon: 'https://api.tianditu.gov.cn/img/map/markerA.png',
      className: 'custom-marker-animated',
      markerStyle: {
        cursor: 'pointer',
        transition: 'filter 0.3s',
      },
      labelStyle: {
        backgroundColor: '#ff4757',
        color: '#ffffff',
        fontSize: '12px',
        padding: '4px 8px',
        borderRadius: '4px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
      },
    }),
  )
}

// 2. Label 测试
const testLabels = () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  testPoints.forEach((point, index) => {
    const labelId = map.createLabel({
      removeable: true,
      position: [point.lng, point.lat],
      text: point.name,
      offset: [0, -30],
      style: {
        backgroundColor: ['#ff4757', '#2ed573', '#1e90ff'][index],
        fontSize: '8px',
        color: '#ffffff',
        borderColor: '#ffffff',
        opacity: '0.9',
      },
      events: {
        click() {
          label.setStyle({
            fontSize: '16px',
          })
        },
      },
    })
    const label = map.getOverlay<TLabel>(labelId)!
    label.addTo(map)
    label.on('mouseout', () => {
      label.setStyle({
        fontSize: '12px',
      })
    })
  })
}

// 3. ContentOverlay 测试
const testContentOverlays = () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  const nativeMap = map.getNativeMap()

  // 1. 基础文本覆盖物
  const textOverlay = new THtmlOverlay({
    position: [testPoints[0].lng, testPoints[0].lat],
    html: `<div class="custom-overlay">
        <h3>HTML 覆盖物</h3>
        <p>支持自定义 HTML 内容</p>
        <button onclick="alert('点击了按钮！')">点击我</button>
      </div>`,
    offset: [300, -340],
    style: {
      background: 'rgba(255, 71, 87, 0.9)',
      color: 'white',
      padding: '8px 12px',
      borderRadius: '4px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    },
    events: {
      click: (e) => {
        ElMessage.success('点击了 基础文本覆盖物')
      },
    },
  })
  textOverlay.addTo(map)

  // 3. Vue 组件覆盖物
  const componentOverlay = new TComponentOverlay({
    position: [testPoints[2].lng, testPoints[2].lat],
    component: MyCustomComponent,
    props: {
      title: 'Vue 组件覆盖物',
      description: '这是一个 Vue 组件示例',
    },
    events: {
      click: (e) => {
        e.stopPropagation()
        ElMessage.success('点击了 Vue 组件覆盖物')
      },
    },
    offset: [400, 140],
  })
  componentOverlay.addTo(map)

  // 4. VNode 覆盖物
  const vnodeOverlay = new TVNodeOverlay({
    position: [testPoints[2].lng + 0.1, testPoints[2].lat - 0.1],
    vnode: h(
      'div',
      {
        class: 'vnode-overlay bg-white p-4 rounded-lg shadow-md',
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
        },
      },
      [
        h('h4', { style: { margin: 0 } }, 'VNode 覆盖物'),
        h('div', { class: 'content' }, [
          h('span', '动态计数: '),
          h('strong', { style: { color: '#409EFF' } }, '42'),
        ]),
        h(
          ElButton,
          {
            type: 'primary',
            size: 'small',
            onClick: () => ElMessage.info('VNode 按钮点击'),
          },
          () => '点击测试',
        ),
      ],
    ),
    offset: [200, 200],
  })
  vnodeOverlay.addTo(map)
}

// 4. SVG Overlay 测试
const testSvgOverlays = () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  // const circleSvg = TSvgOverlay.createCircle({
  //   position: [testPoints[1].lng, testPoints[1].lat],
  //   label: '简单圆形标记',
  //   labelRemoveable: true,
  //   labelStyle: {
  //     color: 'red',
  //   },
  // })
  // map.addOverlay(circleSvg)
  // new TSvgOverlay({
  //   iconName: 'marker-loc',
  //   position: [106.7184, 34.49995],
  //   label: 'svg 标记',
  //   labelRemoveable: true,
  //   zIndexOffset: 9000,
  //   anchor: TAnchorPosition.BOTTOM_CENTER,
  //   iconStyle: {
  //     cursor: 'pointer',
  //   },
  // }).addTo(map)

  const rippleSize = 10000

  // const rippleSvg = TSvgOverlay.createRipple({
  //   position: [106.7184, 34.49995],
  //   // label: '涟漪效果',
  //   labelRemoveable: true,
  //   size: 80,
  //   zIndexOffset: 700,
  //   anchor: TAnchorPosition.CENTER,
  //   container: {
  //     interactive: true,
  //   },
  //   iconStyle: {},
  //   labelStyle: {
  //     // color: 'red',
  //   },
  // })

  // rippleSvg.addTo(map)

  new TAnnotationMarker({
    position: [106.7184, 34.49995],
    zIndexOffset: 900,

    markerOptions: {
      size: 80,
      svgMarkup: TSvgOverlay.generateRippleMarkup(80, { strokeWidth: 2 }),
      // label: 'svg 标记',
      labelRemoveable: true,
      iconStyle: {
        // fill: 'red',
      },
    },
    lineOptions: {
      length: 100,
      angle: 20,
      style: {
        color: '#EBE02D',
        width: 3,
        dashed: true,
      },
    },
    panelOptions: {
      content: h('div', { class: 'custom-panel' }, '这是一个自定义面板'),
      // offset: [-10, -20],
      anchorPosition: 'auto',
    },
  }).addTo(map)

  new TAnnotationMarker({
    position: [106.75742, 34.88641],
    zIndexOffset: 900,

    markerOptions: {
      size: 80,
      svgMarkup: TSvgOverlay.generateRippleMarkup(80, { strokeWidth: 2, stroke: 'red' }),
      // label: 'svg 标记',
      labelRemoveable: true,
      iconStyle: {
        // fill: 'red',
      },
    },
    lineOptions: {
      length: 150,
      angle: -40,
      style: {
        color: 'red',
        width: 3,
        dashed: false,
      },
    },
    panelOptions: {
      content: MyCustomComponent,
      contentProps: {
        title: '组件标题',
        description: '这是一个 Vue 组件',
      },
      contentEvents: {
        click: (e) => {
          ElMessage.info('点击了组件')
        },
      },
      // content: h('div', { class: 'custom-panel' }, '这是一个自定义面板'),}
      offset: [10, 10],
      anchorPosition: 'auto',
    },
  }).addTo(map)

  console.log(metersToPixels(rippleSize, map.getZoom(), map.getCenter().lat))
  map.on('zoomend', (e) => {
    console.log(metersToPixels(rippleSize, map.getZoom(), map.getCenter().lat))
    // rippleSvg.setSize(metersToPixels(rippleSize, map.getZoom(), map.getCenter().lat))
  })
  const marker = new TSvgOverlay({
    iconName: 'marker-loc',
    position: [testPoints[0].lng, testPoints[0].lat],
    label: 'svg 标记',
    labelRemoveable: true,
    removeWithLabel: true,
    labelStyle: {
      color: 'red',
    },
    events: {
      remove: (e, overlay) => {
        ElMessage.info('覆盖物被移除了')
      },
      labelremove: (e, overlay) => {
        ElMessage.info('标签被移除了')
      },
    },
  })
  marker.addTo(map)
  // svgMarkup: `
  //   <svg width="24" height="24" viewBox="0 0 24 24">
  //     <circle)
  // circleSvg.addTo(map)

  // const circleSvg1 = new TSvgOverlay({
  //   position: [105.01541, 32.84134],
  //   text: '起点',
  //   svgMarkup: `
  //     <svg width="20" height="20" viewBox="0 0 20 20">
  //       <circle
  //         cx="10" cy="10" r="8"
  //         fill="#FF4444"
  //         stroke="#FFFFFF"
  //         stroke-width="2"
  //       />
  //     </svg>
  //   `,
  //   offset: [-10, -10],
  //   textOffset: [5, -5],
  //   svgStyle: {
  //     fill: 'red',
  //   },
  //   svgEvents: {
  //     click: () => {
  //       ElMessage.info('点击了圆形SVG标记')
  //     },
  //   },
  //   textEvents: {
  //     click: () => {
  //       ElMessage.info('点击了圆形SVG标记文字')
  //     },
  //   },
  // })
  // circleSvg1.addTo(map)

  // const circleSvg2 = new TSvgOverlay({
  //   position: [102.90634, 33.85154],
  //   text: '终点',
  //   svgMarkup: `
  //     <svg width="20" height="20" viewBox="0 0 20 20">
  //       <circle
  //         cx="10" cy="10" r="8"
  //         fill="#FF4444"
  //         stroke="#FFFFFF"
  //         stroke-width="2"
  //       />
  //     </svg>
  //   `,
  //   offset: [-10, -10],
  //   textOffset: [5, -5],
  //   svgStyle: {
  //     fill: 'red',
  //   },
  //   svgEvents: {
  //     click: () => {
  //       ElMessage.info('点击了圆形SVG标记')
  //     },
  //   },
  //   textEvents: {
  //     click: () => {
  //       ElMessage.info('点击了圆形SVG标记文字')
  //     },
  //   },
  // })
  // circleSvg2.addTo(map)

  // const flyingLine = createFlyingLine(nativeMap, [105.01541, 32.84134], [102.90634, 33.85154], {
  //   text: '飞线示例',
  //   color: '#00f6ff',
  //   duration: 3,
  //   glowColor: '#016886',
  //   glowStrength: 2,
  // })
  // flyingLine.addTo(map)
}

const testPolyline = () => {
  const map = mapRef.value?.getMap()
  if (!map) return

  const polylineId = map.createPolyline({
    path: [
      [104.50195, 34.55634],
      [105.0293, 33.99803],
      [105.38635, 34.18],
    ],
    color: '#ff4d4f',
    weight: 4,
    opacity: 1,
    lineStyle: 'solid',
    label: '普通折线',
    labelStyle: {
      color: 'blue',
      // backgroundColor: 'transparent',
      // borderLine: 0,
    },
    events: {
      click() {
        alert()
      },
    },
  })
  const polyline = map.getOverlay<TPolyline>(polylineId)!
  polyline.enableEdit()

  polyline.onEdit('editstart', (e) => {
    console.log('编辑开始', e)
  })

  polyline.onEdit('editing', (e) => {
    console.log('编辑中', e)
  })

  polyline.onEdit('editend', (e) => {
    console.log('编辑结束', e)
  })
}

const testPolygon = () => {
  const map = mapRef.value?.getMap()
  if (!map) return

  //  const p= new T.Polygon([[103.67545, 34.74949], [104.44998, 34.80364], [103.93363, 34.2197]].map(([lng, lat]) => new T.LngLat(lng, lat)), {
  //     color: '#ff4d4f',
  //     weight: 4,
  //     opacity: 1,
  //     lineStyle: 'solid',

  //   })
  //   map.getNativeMap().addOverLay(p)
  //   p.enableEdit()

  const polygonId = map.createPolygon({
    path: [
      [103.67545, 34.74949],
      [104.44998, 34.80364],
      [103.93363, 34.2197],
    ],
    // color: '#ff4d4f',
    // weight: 4,
    // opacity: 1,
    // lineStyle: 'solid',
    // fillColor: 'transparent',
    label: '普通多边形',
  })

  const polygon = map.getOverlay<TPolygon>(polygonId)!
  polygon.enableEdit()

  geoJSONLayer.value = new TGeoJSONLayer(map, {
    drilldown: {
      enable: true,
      maxDepth: 1,
      inactiveStyle: {
        opacity: 0,
        fillOpacity: 0,
      },
    },
    label: {
      show: true,
      offset: [0, 0],
      style: {
        fontSize: '12px',
        color: '#ffffff',
        fontWeight: 'bold',
        padding: '0px',
        border: 'none',
        boxShadow: 'none',
      },
    },
    onClick(polygon, feature) {
      console.log('点击了', feature.properties.name)
      geoJSONLayer.value?.drillTo(feature)
    },
    onMouseOver(polygon, feature) {
      polygon.setFillColor('#6FF9ED')
    },
    onMouseOut(polygon, feature) {
      polygon.setFillColor('#5CA0FA')
    },
    computeFeatureStyle(feature) {
      return {
        fillColor: feature.properties.name === '渭滨区' ? '#6FF9ED' : '#5CA0FA',
      }
    },
  })

  geoJSONLayer.value?.loadByName(geojsonData as any, '渭滨区')
  // geoJSONLayer.value?.loadByGB(geojsonData as any, '610302')

  // polygon.onEdit('editstart', (e) => {
  //   console.log('编辑开始', e)
  // })

  // polygon.onEdit('editing', (e) => {
  //   console.log('编辑中', e)
  // })

  // polygon.onEdit('editend', (e) => {
  //   console.log('编辑结束', e)
  // })
}
const geoJSONLayer = ref<TGeoJSONLayer | null>(null)
const featureLayer = ref<TFeatureLayer | null>(null)
const onMapReady = (map: TMap) => {
  // 设置初始底图类型为影像图
  map.switchBaseLayer('img')
  currentLayer.value = 'img'
  map.on('mousedown', (e) => {
    console.log('地图点击事件:', e.lnglat)
  })
  map.on('mousemove', (e) => {
    const pixelPos = map.getNativeMap().lngLatToLayerPoint(e.lnglat)
    console.log(pixelPos)
  })

  // testMarkers()
  // testLabels()
  // testContentOverlays()
  testSvgOverlays()
  testPolyline()
  testPolygon()

  // 在地图准备好后初始化标注工具
  markTool.value = map.createPointMarkerTool({
    // label: '默认的标题',
    iconName: 'marker-loc',
    // iconUrl: 'https://api.tianditu.gov.cn/img/map/markerA.png',
    removeable: true,
    onRemove(marker) {
      console.log('移除标注', marker)
    },
    // iconUrl: 'https://api.tianditu.gov.cn/img/map/markerA.png',
    // svgMarkup: `<svg width="40" height="40" viewBox="0 0 40 40">
    //     <path
    //       d="M20 0C12.7 0 7 5.7 7 13c0 7.9 11.9 25.5 12.4 ******** 1.2.7 *******-.1.2-.2.3-.3C22 38.5 33 20.9 33 13c0-7.3-5.7-13-13-13zm0 18c-2.8 0-5-2.2-5-5s2.2-5 5-5 5 2.2 5 5-2.2 5-5 5z"
    //       fill="#1e90ff"
    //       stroke="#FFFFFF"
    //       stroke-width="2"
    //     />
    //   </svg>`,
    enableGeocoding: true,
    autoSetAddress: true,
    updateAddressOnDrag: true,
    geocodeDebounceTime: 0,
    // text: '一个标注 ',
    mode: TPointMarkerMode.FOLLOW,
    // width: 20,
    // height: 30,
    // textOffset: [10, 0],
    iconStyle: {
      fill: 'blue',
    },
    onMark: (position) => {
      console.log('标注位置:', position)
      markersCount.value = markTool.value?.getMarkers().length ?? 0
      updateMarkToolState()
    },
    onComplete: (markers) => {
      console.log('完成标注，共标注点数:', markers.length)
      markersCount.value = markers.length
    },
    onGeocodeComplete(marker, address, detail) {
      console.log(marker.getPosition())
      console.log('地理编码完成，地址:', address, '详情:', detail)
    },
  })

  // 初始化多边形工具
  polygonTool.value = map.createPolygonTool({
    color: '#1890ff',
    weight: 2,
    opacity: 1,
    fillColor: '#1890ff',
    fillOpacity: 0.3,
    lineStyle: 'solid',
    showArea: true,
    onRemove(polygon) {
      console.log('多边形被移除:', polygon)
    },
    onComplete(polygons) {},
    onAreaChange(area) {
      console.log('面积变化，当前面积:', area)
    },
  })

  // 初始化折线工具
  polylineTool.value = map.createPolylineTool({
    color: '#ff4d4f',
    weight: 3,
    opacity: 1,
    lineStyle: 'solid',
    showDistance: true,
    onComplete(polylines) {
      console.log(
        '绘制完成，共绘制折线数:',
        polylines.forEach((polyline) => {
          console.log(polyline.getLngLats())
        }),
      )
    },
    onRemove(polyline) {
      console.log('折线被移除:', polyline)
    },
    onDistanceChange(distance) {
      console.log('距离变化，当前距离:', distance)
    },
  })

  // polylineTool.value.clear()

  infoWindowId.value = map.createInfoWindow({
    content: '这是一个信息窗口',
    // unstyled: true,
    // hideCloseButton: true,
    windowEvents: {
      close: () => {
        ElMessage.info('信息窗口已关闭')
      },
    },
  })

  // map.getInfoWindow(id)?.openAt([testPoints[0].lng, testPoints[0].lat])
}

const drillUp = () => {
  geoJSONLayer.value?.drillUp()
}

const onMapError = (error: Error) => {
  console.error('地图加载失败:', error)
}

// 获取当前缩放级别
const handleGetZoom = () => {
  if (!mapRef.value) return

  const zoom = mapRef.value.getZoom()
  ElMessage.info(`当前缩放级别: ${zoom}`)
}

const clearMarks = () => {
  if (markTool.value) {
    markTool.value.clear()
    markersCount.value = 0
    ElMessage.success('已清除所有标注')
  }
}

const hasMarkers = computed(() => {
  return (markTool.value?.getMarkers().length ?? 0) > 0
})

const handleGeocodeTest = () => {
  geocodeDialogVisible.value = true
}

const performGeocode = async () => {
  if (!mapRef.value || !addressInput.value) return

  try {
    const map = mapRef.value.getMap()
    if (!map) return

    const result = await map.geocode(addressInput.value)
    const point = result.getLocationPoint()
    const address = result.getAddress()
    const detail = result.getAddressComponent()

    // 在地图上标记位置
    map.addOverlay(
      new TMarker({
        position: [point.lng, point.lat],
        label: address,
        draggable: true,
      }),
    )

    // 将地图中心移动到该位置
    map.setCenter({
      lng: point.lng,
      lat: point.lat,
    })
    map.setZoom(15)

    // 显示结果
    ElMessage.success(`解析成功：${address}`)
    console.log('地址详情：', detail)

    geocodeDialogVisible.value = false
    addressInput.value = ''
  } catch (error) {
    ElMessage.error('地址解析失败')
    console.error('地址解析错误：', error)
  }
}

const handleReverseGeocodeTest = async () => {
  if (!mapRef.value) return

  const map = mapRef.value.getMap()
  if (!map) return

  try {
    // 获取地图中心点
    const center = map.getCenter()
    const result = await map.reverseGeocode(new T.LngLat(center.lng, center.lat))

    // 获取地址信息
    const address = result.getAddress()
    const detail = result.getAddressComponent()

    // 在地图中心添加标记
    map.addOverlay(
      new TMarker({
        position: [center.lng, center.lat],
        label: address,
        draggable: true,
      }),
    )

    // 显示结果
    ElMessage.success(`当前位置：${address}`)
    console.log('位置详情：', detail)
  } catch (error) {
    ElMessage.error('逆地址解析失败')
    console.error('逆地址解析错误：', error)
  }
}

// 多边形工具控制
const togglePolygonTool = () => {
  if (!polygonTool.value) return
  if (polygonToolActive.value) {
    polygonTool.value.stop()
  } else {
    polygonTool.value.start()
  }
  polygonToolActive.value = !polygonToolActive.value
}

// 折线工具控制
const togglePolylineTool = () => {
  if (!polylineTool.value) return
  if (polylineToolActive.value) {
    polylineTool.value.stop()
  } else {
    polylineTool.value.start()
  }
  polylineToolActive.value = !polylineToolActive.value
}

const style = document.createElement('style')

style.textContent += `
  @keyframes dash {
    to {
      stroke-dashoffset: 0;
    }
  }

  .flying-line {
    animation: dash 3s linear infinite;
  }
`
document.head.appendChild(style)

// 待优化
const createFlyingLine = (
  nativeMap: any,
  startPoint: [number, number],
  endPoint: [number, number],
  options: {
    color?: string
    duration?: number
    text?: string
    dashArray?: string
    glowColor?: string
    glowStrength?: number
  } = {},
) => {
  const {
    color = '#00f6ff',
    duration = 3,
    text = '',
    dashArray = '20 80',
    glowColor = '#016886',
    glowStrength = 2,
  } = options

  const p1 = new T.LngLat(startPoint[0], startPoint[1])
  const p2 = new T.LngLat(endPoint[0], endPoint[1])

  const start = nativeMap.lngLatToContainerPoint(p1)
  const end = nativeMap.lngLatToContainerPoint(p2)

  const distance = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2))
  const offsetX = start.x - distance
  const offsetY = start.y - distance
  const adjustedStartX = start.x - offsetX
  const adjustedStartY = start.y - offsetY
  const adjustedEndX = end.x - offsetX
  const adjustedEndY = end.y - offsetY

  // 计算控制点
  const controlX = (adjustedStartX + adjustedEndX) / 2
  const controlY = Math.min(adjustedStartY, adjustedEndY) - 50 // 向上偏移
  const d = `M${adjustedStartX},${adjustedStartY} Q${controlX},${controlY} ${adjustedEndX},${adjustedEndY}`

  const flyingLine = new TSvgOverlay({
    position: [p1.lng, p1.lat],
    svgMarkup: `
      <svg width="${2 * distance}" height="${2 * distance}" viewBox="0 0 ${2 * distance} ${2 * distance}">
        <defs>
          <linearGradient id="flyline-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:0" />
            <stop offset="50%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${color};stop-opacity:0" />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="${glowStrength}" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        <path
          d="${d}"
          stroke="${glowColor}"
          stroke-width="2"
          fill="none"
          filter="url(#glow)"
        />
        <path
          d="${d}"
          stroke="url(#flyline-gradient)"
          stroke-width="2"
          fill="none"
          style="stroke-dasharray: ${dashArray}; animation: fly ${duration}s linear infinite;"
        />
        <style>
          @keyframes fly {
            from {
              stroke-dashoffset: 100;
            }
            to {
              stroke-dashoffset: 0;
            }
          }
        </style>
      </svg>
    `,
    labelOffset: [5, -5],
  })

  return flyingLine
}

// 添加自定义图层的引用
const customLayer = ref<TOverlayLayer | null>(null)

// 在默认图层添加标记
const addToDefaultLayer = () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  const markerId = map.createMarker<number>({
    userData: 5,
    position: [testPoints[1].lng, testPoints[1].lat],
    label: '默认图层标记',
    icon: 'https://api.tianditu.gov.cn/img/map/markerA.png',
    draggable: true,
    events: {
      click: () => {
        ElMessage.info('点击了默认图层标记')
      },
    },
  })

  map.getOverlay(markerId)?.setUserData(8)
  console.log(map.getOverlay(markerId))
  ElMessage.success('已在默认图层添加标记')
}

// 在自定义图层添加标记
const addToCustomLayer = () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  // 如果自定义图层不存在，先创建
  if (!customLayer.value) {
    customLayer.value = map.createOverlayLayer({ id: 'test-layer' })
  }

  // customLayer.value.createMarker({
  //   position: [testPoints[1].lng, testPoints[1].lat],
  //   label: '自定义图层标记',
  //   icon: 'https://api.tianditu.gov.cn/img/map/markerA.png',
  //   events: {
  //     click: () => {
  //       ElMessage.info('点击了自定义图层标记')
  //     },
  //   },
  // })

  // ElMessage.success('已在自定义图层添加标记')

  customLayer.value.createSvgOverlay({
    position: [testPoints[1].lng, testPoints[1].lat],
    label: '自定义图层标记',
    iconName: 'marker-loc',
    labelRemoveable: true,
    removeWithLabel: true,
    events: {
      labelremove: (e, overlay) => {
        ElMessage.info('标签被移除了')
      },
      remove: (e, overlay) => {
        ElMessage.info('覆盖物被移除了')
      },
      click: () => {
        ElMessage.info('点击了自定义图层标记')
      },
    },
  })

  ElMessage.success('已在自定义图层添加标记')
}

// 清除默认图层
const clearDefaultLayer = () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  map.clearOverlays()
  ElMessage.success('已清除默认图层')
}

// 移除自定义图层
const removeCustomLayer = () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map || !customLayer.value) return

  map.removeOverlayLayer('test-layer')
  customLayer.value = null
  ElMessage.success('已移除自定义图层')
}

// 添加 InfoWindow 相关的响应式变量
const infoWindowId = ref<string>()

// 添加示例方法
const showInfoWindow = () => {
  if (!infoWindowId.value || !mapRef.value) return
  const map = mapRef.value.getMap()

  const infoWindow = map.getInfoWindow(infoWindowId.value)
  infoWindow?.setContent(
    h('div', { class: 'custom-info' }, [
      h('h3', '自定义内容'),
      h(
        ElButton,
        {
          onClick: () => ElMessage.success('点击了按钮'),
        },
        () => '点击我',
      ),
    ]),
  )

  infoWindow?.openAt([testPoints[0].lng, testPoints[0].lat])
}

const showComponentInInfoWindow = () => {
  if (!infoWindowId.value || !mapRef.value) return
  const map = mapRef.value.getMap()
  const infoWindow = map.getInfoWindow(infoWindowId.value)

  infoWindow?.setContent(
    MyCustomComponent,
    {
      title: '组件标题',
      description: '这是一个 Vue 组件',
    },
    {
      click: () => ElMessage.success('确认事件触发'),
    },
  )

  infoWindow?.openAt([testPoints[0].lng, testPoints[0].lat])
}

// 添加下钻对话框状态
const drillByNameDialogVisible = ref(false)
const drillNameInput = ref('')
const drillByGBDialogVisible = ref(false)
const drillGBInput = ref('')

// 显示按名称下钻对话框
const showDrillByNameDialog = () => {
  drillByNameDialogVisible.value = true
}

// 执行按名称下钻
const performDrillByName = async () => {
  if (!drillNameInput.value || !geoJSONLayer.value) {
    ElMessage.warning('请输入有效的区域名称')
    return
  }

  try {
    const success = await geoJSONLayer.value.drillToByName(drillNameInput.value)
    if (success) {
      ElMessage.success(`成功下钻到 ${drillNameInput.value}`)
      drillByNameDialogVisible.value = false
      drillNameInput.value = ''
    } else {
      ElMessage.error(`未找到名称为 "${drillNameInput.value}" 的区域`)
    }
  } catch (error) {
    console.error('下钻失败:', error)
    ElMessage.error('下钻操作失败')
  }
}

// 显示按GB编码下钻对话框
const showDrillByGBDialog = () => {
  drillByGBDialogVisible.value = true
}

// 执行按GB编码下钻
const performDrillByGB = async () => {
  if (!drillGBInput.value || !geoJSONLayer.value) {
    ElMessage.warning('请输入有效的GB编码')
    return
  }

  try {
    const success = await geoJSONLayer.value.drillToByGB(drillGBInput.value)
    if (success) {
      ElMessage.success(`成功下钻到GB编码为 ${drillGBInput.value} 的区域`)
      drillByGBDialogVisible.value = false
      drillGBInput.value = ''
    } else {
      ElMessage.error(`未找到GB编码为 "${drillGBInput.value}" 的区域`)
    }
  } catch (error) {
    console.error('下钻失败:', error)
    ElMessage.error('下钻操作失败')
  }
}

// ==================== TFeatureLayer 测试方法 ====================

// 测试多边形要素 - 加载宝鸡市行政区划数据
const testFeatureLayerPolygons = async () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  try {
    if (featureLayer.value) {
      featureLayer.value.clear()
    }

    featureLayer.value = new TFeatureLayer(map, {
      polygonStyle: {
        fillColor: '#6FF9ED',
        fillOpacity: 0.4,
        color: '#1ECAD3',
        weight: 2,
        opacity: 0.8,
      },
      events: {
        onClick: (feature, geometryType, overlay) => {
          const name = feature.properties?.name || '未知区域'
          ElMessage.info(`点击了多边形要素: ${name}`)
          console.log('多边形要素详情:', feature)
        },
        onMouseOver: (feature, geometryType, overlay) => {},
        onMouseOut: (feature, geometryType, overlay) => {},
      },
      fieldMapping: {
        nameField: 'name',
        codeField: 'gb',
      },
    })

    // 加载宝鸡市行政区划数据
    const response = await fetch('/baoji.geojson')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const geojsonData = await response.json()

    await featureLayer.value.load(geojsonData, true)

    ElMessage.success('多边形要素图层加载成功！')
    console.log('已加载的要素数量:', geojsonData.features.length)
  } catch (error) {
    console.error('加载多边形要素失败:', error)
    ElMessage.error('加载多边形要素失败')
  }
}

// 测试点要素 - 加载森林公园数据
const testFeatureLayerPoints = async () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  try {
    if (featureLayer.value) {
      featureLayer.value.clear()
    }

    featureLayer.value = new TFeatureLayer(map, {
      pointStyle: {
        labelText: (feature) => feature.properties.name,
        labelStyle: {
          background: 'red',
          color: '#fff',
        },
        type: 'svg',
        size: [32, 32],
        svgOptions: {
          iconName: 'marker-loc',
          labelRemoveable: true,
          removeWithLabel: true,
        },
      },

      events: {
        onClick: (feature, geometryType, overlay) => {
          const name = feature.properties?.name || feature.properties?.name_short || '未知地点'
          const county = feature.properties?.county || '未知区县'
          ElMessage.info(`点击了点要素: ${name} (${county})`)
          console.log('点要素详情:', feature)
        },
      },
      fieldMapping: {
        nameField: 'name',
      },
      geometryTypes: ['Point', 'MultiPoint'], // 只加载点要素
    })

    // 加载森林公园数据
    const response = await fetch('/forest_point.geojson')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const geojsonData = await response.json()

    await featureLayer.value.load(geojsonData, true)

    ElMessage.success('点要素图层加载成功！')
    console.log('已加载的要素数量:', geojsonData.features.length)
  } catch (error) {
    console.error('加载点要素失败:', error)
    ElMessage.error('加载点要素失败')
  }
}

// 测试线要素 - 加载河流线数据
const testFeatureLayerLines = async () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  try {
    if (featureLayer.value) {
      featureLayer.value.clear()
    }

    featureLayer.value = new TFeatureLayer(map, {
      lineStyle: {
        color: '#28ade1',
        weight: 2,
        opacity: 0.8,
        showArrow: false,
      },
      events: {
        onClick: (feature, geometryType, overlay) => {
          const name = feature.properties?.name || feature.properties?.NAME || '未知河流'
          ElMessage.info(`点击了线要素: ${name}`)
          console.log('线要素详情:', feature)
        },
        onMouseOver: (feature, geometryType, overlay) => {},
        onMouseOut: (feature, geometryType, overlay) => {},
      },
      fieldMapping: {
        nameField: 'name',
      },
      geometryTypes: ['LineString', 'MultiLineString'], // 只加载线要素
    })

    // 加载河流线数据
    const response = await fetch('/river_line.geojson')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const geojsonData = await response.json()

    await featureLayer.value.load(geojsonData, true)

    ElMessage.success('线要素图层加载成功！')
    console.log('已加载的要素数量:', geojsonData.features.length)
  } catch (error) {
    console.error('加载线要素失败:', error)
    ElMessage.error('加载线要素失败')
  }
}

// 测试河流要素 - 同时加载河流面和河流线
const testFeatureLayerRivers = async () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  try {
    if (featureLayer.value) {
      featureLayer.value.clear()
    }

    // 创建新的要素图层，支持河流面和河流线
    featureLayer.value = new TFeatureLayer(map, {
      polygonStyle: {
        fillColor: '#28ade1',
        fillOpacity: 0.6,
        color: '#1976d2',
        weight: 2,
        opacity: 0.8,
      },
      lineStyle: {
        color: '#0d47a1',
        weight: 4,
        opacity: 0.9,
        showArrow: false,
      },
      events: {
        onClick: (feature, geometryType, overlay) => {
          const name = feature.properties?.name || feature.properties?.NAME || '未知河流'
          const type = geometryType.includes('Polygon') ? '河流面' : '河流线'
          ElMessage.info(`点击了${type}: ${name}`)
          console.log('河流要素详情:', feature, '几何类型:', geometryType)
        },
      },
      fieldMapping: {
        nameField: 'name',
      },
    })

    // 并行加载河流面和河流线数据
    const areaResponse = await fetch('/river_area.geojson')

    if (areaResponse.ok) {
      const geojsonData = await areaResponse.json()
      await featureLayer.value.load(geojsonData, true)
      ElMessage.success('河流要素图层加载成功！')
    }
  } catch (error) {
    console.error('加载河流要素失败:', error)
    ElMessage.error('加载河流要素失败')
  }
}

// 测试混合要素 - 加载保护区数据（包含多边形和点）
const testFeatureLayerMixed = async () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  try {
    if (featureLayer.value) {
      featureLayer.value.clear()
    }

    // 创建新的要素图层，支持多种几何类型
    featureLayer.value = new TFeatureLayer(map, {
      polygonStyle: {
        fillColor: '#0c9d55',
        fillOpacity: 0.6,
        color: '#28ade1',
        weight: 2,
        opacity: 0.8,
        labelText: (feature) => feature.properties.code,
        labelOffset: [0, 0],
        labelStyle: {
          fontSize: '12px',
          fontWeight: 'bold',
          border: 'none',
          boxShadow: 'none',
        },
      },
      pointStyle: {
        type: 'marker',
        icon: 'https://api.tianditu.gov.cn/img/map/markerA.png',
        size: [24, 24],
      },
      events: {
        onClick: (feature, geometryType, overlay) => {
          const name = feature.properties?.name || feature.properties?.code || '未知保护区'
          const type = geometryType === 'Point' ? '点要素' : '面要素'
          ElMessage.info(`点击了${type}: ${name}`)
          console.log('要素详情:', feature, '几何类型:', geometryType)
        },
        onMouseOver: (feature, geometryType, overlay) => {
          if (geometryType === 'Polygon') {
            ;(overlay as TPolygon).setFillColor('#FFD700')
          }
        },
        onMouseOut: (feature, geometryType, overlay) => {
          if (geometryType === 'Polygon') {
            ;(overlay as TPolygon).setFillColor('#0c9d55')
          }
        },
      },
      fieldMapping: {
        nameField: 'name',
        codeField: 'code',
      },
      loadAllGeometryTypes: true, // 加载所有几何类型
    })

    // 加载保护区面数据和点数据
    const [areaResponse, pointResponse] = await Promise.all([
      fetch('/protected_area.geojson'),
      fetch('/protected_point.geojson'),
    ])

    const combinedFeatures: any[] = []

    if (areaResponse.ok) {
      const areaData = await areaResponse.json()
      combinedFeatures.push(...areaData.features)
    }

    if (pointResponse.ok) {
      const pointData = await pointResponse.json()
      combinedFeatures.push(...pointData.features)
    }

    if (combinedFeatures.length > 0) {
      const combinedData = {
        type: 'FeatureCollection' as const,
        features: combinedFeatures,
      }
      await featureLayer.value.load(combinedData, true)
    }

    ElMessage.success('混合要素图层加载成功！')
    console.log('已加载的要素数量:', combinedFeatures.length)
  } catch (error) {
    console.error('加载混合要素失败:', error)
    ElMessage.error('加载混合要素失败')
  }
}

// 测试WKT数据
const testFeatureLayerWKT = async () => {
  if (!mapRef.value) return
  const map = mapRef.value.getMap()
  if (!map) return

  try {
    if (featureLayer.value) {
      featureLayer.value.clear()
    }

    if (!thematicRemoteList.value || thematicRemoteList.value.length === 0) {
      ElMessage.warning('暂无WKT数据，请等待数据加载完成')
      return
    }

    featureLayer.value = new TFeatureLayer(map, {
      polygonStyle: {
        fillColor: '#FF6B35',
        fillOpacity: 0.5,
        color: '#FF4757',
        weight: 2,
        opacity: 0.8,
        labelText: (feature) => feature.properties.analysisText,
        labelOffset: [0, 0],
        labelStyle: {
          fontSize: '12px',
          fontWeight: 'bold',
          border: 'none',
          boxShadow: 'none',
        },
      },
      pointStyle: {
        type: 'marker',
        icon: 'https://api.tianditu.gov.cn/img/map/markerA.png',
        size: [28, 28],
      },
      lineStyle: {
        color: '#FF4757',
        weight: 3,
        opacity: 0.8,
        showArrow: false,
      },
      events: {
        onClick: (feature, geometryType, overlay) => {
          const props = feature.properties
          const name = props?.analysisText || props?.id || '未知要素'
          ElMessage.info(`点击了WKT要素: ${name}`)

          console.log('WKT要素详情:', {
            几何类型: geometryType,
            属性: props,
            面积: props?.area,
            数据类型: props?.dataType,
            区域代码: props?.regionCode,
            状态: props?.status,
            前期图片: props?.beforePicPath,
            后期图片: props?.endPicPath,
          })
        },
      },
      fieldMapping: {
        nameField: 'analysisText',
        codeField: 'regionCode',
      },
      loadAllGeometryTypes: true,
    })

    const geojsonData = {
      type: 'FeatureCollection' as const,
      features: thematicRemoteList.value,
    }

    await featureLayer.value.load(geojsonData, true)

    ElMessage.success('WKT数据图层加载成功！')
    console.log('已加载的WKT要素数量:', thematicRemoteList.value.length)
  } catch (error) {
    console.error('加载WKT数据失败:', error)
    ElMessage.error('加载WKT数据失败')
  }
}

// 清除要素图层
const clearFeatureLayer = () => {
  if (!featureLayer.value) return

  featureLayer.value.clear()
  featureLayer.value = null
  ElMessage.success('已清除要素图层')
}
</script>

<style scoped>
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.custom-marker-animated {
  position: relative;
}

.custom-marker-animated::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid #ff4757;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 1.5s ease-out infinite;
  z-index: -1;
}

@keyframes ripple {
  0% {
    width: 100%;
    height: 100%;
    opacity: 1;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}

.custom-marker-animated:hover {
  filter: brightness(1.2);
}

.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.map-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mark-controls {
  margin-top: 10px;
}

.layer-controls {
  margin-top: 10px;
}

/* 地图控制按钮样式 */
.map-controls {
  position: absolute;
  right: 20px;
  top: 20px;
  z-index: 401;
  background-color: white;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.tdt-control-overview) {
  border: 2px solid #dcdfe6 !important;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.tdt-control-overview .tdt-overview-map) {
  border: none !important;
}

.custom-search-result {
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.result-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.result-address {
  font-size: 12px;
  color: #909399;
  margin-left: 24px;
}

.result-info {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}

.distance {
  font-size: 12px;
  color: #409eff;
  background: #ecf5ff;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 自定义标记动画 */
@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.custom-marker-animated {
  transition: all 0.3s ease;
}

.custom-marker-animated:hover {
  transform: scale(1.2);
}

/* 自定义覆盖物样式 */
.custom-overlay {
  text-align: center;
}

.custom-overlay h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.custom-overlay p {
  margin: 0 0 12px 0;
  color: #666;
}

.custom-overlay button {
  background: #1e90ff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.custom-overlay button:hover {
  background: #1873cc;
}

.control-group {
  margin-top: 10px;
  display: flex;
  gap: 8px;
}

.draw-controls {
  margin-top: 10px;
}

.layer-test-controls {
  margin-top: 10px;
}

.info-window-controls {
  margin-left: 8px;
}

.custom-info {
  padding: 12px;
}

.custom-info h3 {
  margin: 0 0 8px;
}

/* 添加下钻控制按钮组样式 */
.drill-controls {
  margin-top: 10px;
  margin-left: 8px;
}

/* TFeatureLayer 测试按钮组样式 */
.feature-layer-controls {
  margin-top: 10px;
}

.feature-layer-advanced-controls {
  margin-top: 10px;
}
</style>
