<template>
  <div class="topsis-container">
    <div class="input-section">
      <h3>输入数据</h3>

      <div class="config-row">
        <div class="config-item">
          <label>方案数量:</label>
          <el-input-number
            v-model="schemeCount"
            :min="2"
            :max="10"
            @change="initializeData"
          />
        </div>
        <div class="config-item">
          <label>指标数量:</label>
          <el-input-number
            v-model="criteriaCount"
            :min="2"
            :max="8"
            @change="initializeData"
          />
        </div>
        <div class="config-item">
          <el-button
            type="primary"
            @click="loadExample"
            >加载示例数据</el-button
          >
          <el-button @click="calculate">计算分析</el-button>
        </div>
      </div>

      <div class="criteria-types">
        <h4>指标类型设置</h4>
        <div class="type-row">
          <div
            v-for="(_, index) in criteriaTypes"
            :key="index"
            class="type-item"
          >
            <label>指标{{ index + 1 }}:</label>
            <el-select v-model="criteriaTypes[index]">
              <el-option
                label="效益型(越大越好)"
                value="max"
              />
              <el-option
                label="成本型(越小越好)"
                value="min"
              />
            </el-select>
          </div>
        </div>
      </div>

      <div class="matrix-input">
        <h4>决策矩阵</h4>
        <table class="matrix-table">
          <thead>
            <tr>
              <th>方案</th>
              <th
                v-for="j in criteriaCount"
                :key="j"
              >
                指标{{ j }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="i in schemeCount"
              :key="i"
            >
              <td>方案{{ i }}</td>
              <td
                v-for="j in criteriaCount"
                :key="j"
              >
                <el-input-number
                  v-model="decisionMatrix[i - 1][j - 1]"
                  :precision="2"
                  size="small"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div
      v-if="results"
      class="results-section"
    >
      <h3>分析结果</h3>

      <div class="flex">
        <div class="result-item">
          <h4>熵权权重</h4>
          <div class="weights-display">
            <div
              v-for="(weight, index) in results.weights"
              :key="index"
              class="weight-item"
            >
              <span>指标{{ index + 1 }}: {{ (weight * 100).toFixed(2) }}%</span>
            </div>
          </div>
        </div>
      </div>

      <div class="flex">
        <div class="result-item flex-1">
          <h4>方案排名</h4>
          <table class="ranking-table">
            <thead>
              <tr>
                <th>排名</th>
                <th>方案</th>
                <th>TOPSIS得分</th>
                <th>相对接近度</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in results.ranking"
                :key="item.originalIndex"
              >
                <td>{{ item.rank }}</td>
                <td>方案{{ item.originalIndex + 1 }}</td>
                <td>{{ item.score.toFixed(4) }}</td>
                <td>{{ (item.score * 100).toFixed(2) }}%</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="result-item flex-1">
          <h4>三维空间可视化</h4>
          <p class="chart-description">展示各方案在加权决策空间中的分布以及与理想解的关系</p>
          <div
            ref="chartContainer"
            class="chart-container"
          ></div>
        </div>
      </div>

      <!-- 详细计算过程 -->
      <el-collapse
        v-model="activeCollapse"
        class="detail-collapse"
      >
        <el-collapse-item
          title="查看详细计算过程"
          name="details"
        >
          <div
            class="detail-section"
            v-if="false"
          >
            <h5>标准化矩阵</h5>
            <table class="detail-table">
              <thead>
                <tr>
                  <th>方案</th>
                  <th
                    v-for="j in criteriaCount"
                    :key="j"
                  >
                    指标{{ j }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(row, i) in results.normalizedMatrix"
                  :key="i"
                >
                  <td>方案{{ i + 1 }}</td>
                  <td
                    v-for="(value, j) in row"
                    :key="j"
                  >
                    {{ value.toFixed(4) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="flex">
            <div class="detail-section">
              <h5>加权决策矩阵</h5>
              <table class="detail-table">
                <thead>
                  <tr>
                    <th>方案</th>
                    <th
                      v-for="j in criteriaCount"
                      :key="j"
                    >
                      指标{{ j }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(row, i) in results.weightedMatrix"
                    :key="i"
                  >
                    <td>方案{{ i + 1 }}</td>
                    <td
                      v-for="(value, j) in row"
                      :key="j"
                    >
                      {{ value.toFixed(4) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 理想解 -->
            <div class="detail-section">
              <h5>理想解与负理想解</h5>
              <table class="detail-table">
                <thead>
                  <tr>
                    <th>解类型</th>
                    <th
                      v-for="j in criteriaCount"
                      :key="j"
                    >
                      指标{{ j }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>正理想解</td>
                    <td
                      v-for="(value, j) in results.idealSolution"
                      :key="j"
                    >
                      {{ value.toFixed(4) }}
                    </td>
                  </tr>
                  <tr>
                    <td>负理想解</td>
                    <td
                      v-for="(value, j) in results.negativeIdealSolution"
                      :key="j"
                    >
                      {{ value.toFixed(4) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 距离计算 -->
            <div class="detail-section">
              <h5>距离计算</h5>
              <table class="detail-table">
                <thead>
                  <tr>
                    <th>方案</th>
                    <th>到正理想解距离</th>
                    <th>到负理想解距离</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(_, i) in results.positiveDistances"
                    :key="i"
                  >
                    <td>方案{{ i + 1 }}</td>
                    <td>{{ results.positiveDistances[i].toFixed(4) }}</td>
                    <td>{{ results.negativeDistances[i].toFixed(4) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { entropyTopsis } from './main'
import * as echarts from 'echarts'
import 'echarts-gl'

// 响应式数据
const schemeCount = ref(6)
const criteriaCount = ref(4)
const criteriaTypes = ref<('max' | 'min')[]>(['max', 'max', 'max', 'max']) // 指标类型
const decisionMatrix = ref<number[][]>([]) // 决策矩阵
const results = ref<any>(null)
const activeCollapse = ref<string[]>([])
const chartContainer = ref<HTMLElement>()

const initializeData = () => {
  criteriaTypes.value = Array(criteriaCount.value).fill('max')

  decisionMatrix.value = Array(schemeCount.value)
    .fill(null)
    .map(() => Array(criteriaCount.value).fill(0))

  results.value = null
}

const loadExample = () => {
  schemeCount.value = 6
  criteriaCount.value = 4
  // 全部取效益型
  criteriaTypes.value = ['max', 'max', 'max', 'max']

  decisionMatrix.value = [
    [0.92, 5.5, 1, 0.75], // 供应商A
    [0.85, 6, 2.52, 0.54], // 供应商B
    [0.87, 7.6, 4.6, 0.7], // 供应商C
    [0.9, 8.2, 5.42, 0.82], // 供应商D
    [0.7, 7.1, 2.2, 0.6], // 供应商E
    [0.78, 4.2, 1.8, 0.45], // 供应商F
  ]

  ElMessage.success('已加载供应商选择示例数据')
}

const calculate = () => {
  try {
    if (!decisionMatrix.value || decisionMatrix.value.length === 0) {
      ElMessage.error('请先输入决策矩阵数据')
      return
    }

    // 检查是否有空值
    for (let i = 0; i < decisionMatrix.value.length; i++) {
      for (let j = 0; j < decisionMatrix.value[i].length; j++) {
        if (decisionMatrix.value[i][j] === null || decisionMatrix.value[i][j] === undefined) {
          ElMessage.error(`方案${i + 1}的指标${j + 1}数据不能为空`)
          return
        }
      }
    }

    // 调用熵权TOPSIS算法
    results.value = entropyTopsis(decisionMatrix.value, criteriaTypes.value)

    // 渲染三维散点图
    nextTick(() => {
      renderChart()
    })

    ElMessage.success('计算完成！')
  } catch (error: any) {
    ElMessage.error(`计算错误: ${error.message}`)
    console.error('TOPSIS计算错误:', error)
  }
}

const renderChart = () => {
  if (!chartContainer.value || !results.value) return

  const chart = echarts.init(chartContainer.value)

  const weightedMatrix = results.value.weightedMatrix
  const idealSolution = results.value.idealSolution
  const negativeIdealSolution = results.value.negativeIdealSolution
  const scores = results.value.scores

  // 如果指标数量大于3，取前3个主要指标进行可视化
  // 需要降维
  const visualDimensions = Math.min(3, weightedMatrix[0].length)

  const alternativeData = weightedMatrix.map((row: number[], index: number) => {
    const point = row.slice(0, visualDimensions)
    while (point.length < 3) {
      point.push(0)
    }
    return {
      value: point,
      name: `方案${index + 1}`,
      score: scores[index],
      itemStyle: {
        color: getColorByScore(scores[index]),
      },
    }
  })

  const idealPoint = idealSolution.slice(0, visualDimensions)
  while (idealPoint.length < 3) {
    idealPoint.push(0)
  }

  const negativeIdealPoint = negativeIdealSolution.slice(0, visualDimensions)
  while (negativeIdealPoint.length < 3) {
    negativeIdealPoint.push(0)
  }

  const option = {
    title: {
      text: '熵权TOPSIS三维空间分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        if (params.seriesName === '方案') {
          return `${params.data.name}<br/>
                  坐标: [${params.data.value.map((v: number) => v.toFixed(4)).join(', ')}]<br/>
                  TOPSIS得分: ${params.data.score.toFixed(4)}`
        } else {
          return `${params.seriesName}<br/>
                  坐标: [${params.data.value.map((v: number) => v.toFixed(4)).join(', ')}]`
        }
      },
    },
    legend: {
      data: ['方案', '正理想解', '负理想解'],
      top: 30,
    },
    grid3D: {
      boxWidth: 200,
      boxHeight: 200,
      boxDepth: 200,
      viewControl: {
        projection: 'perspective',
        autoRotate: false,
        autoRotateSpeed: 5,
        distance: 300,
      },
      light: {
        main: {
          intensity: 1.2,
          shadow: true,
        },
        ambient: {
          intensity: 0.3,
        },
      },
    },
    xAxis3D: {
      name: `指标1 (权重: ${(results.value.weights[0] * 100).toFixed(1)}%)`,
      type: 'value',
    },
    yAxis3D: {
      name:
        visualDimensions > 1
          ? `指标2 (权重: ${(results.value.weights[1] * 100).toFixed(1)}%)`
          : '指标2',
      type: 'value',
    },
    zAxis3D: {
      name:
        visualDimensions > 2
          ? `指标3 (权重: ${(results.value.weights[2] * 100).toFixed(1)}%)`
          : '指标3',
      type: 'value',
    },
    series: [
      {
        name: '方案',
        type: 'scatter3D',
        data: alternativeData,
        symbolSize: 12,
        emphasis: {
          itemStyle: {
            borderColor: '#000',
            borderWidth: 2,
          },
        },
      },
      {
        name: '正理想解',
        type: 'scatter3D',
        data: [
          {
            value: idealPoint,
            name: '正理想解',
          },
        ],
        symbolSize: 20,
        itemStyle: {
          color: '#ff4757',
          opacity: 0.9,
        },
        symbol: 'diamond',
      },
      {
        name: '负理想解',
        type: 'scatter3D',
        data: [
          {
            value: negativeIdealPoint,
            name: '负理想解',
          },
        ],
        symbolSize: 20,
        itemStyle: {
          color: '#2f3542',
          opacity: 0.9,
        },
        symbol: 'triangle',
      },
    ],
  }

  chart.setOption(option)

  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 根据得分获取颜色
const getColorByScore = (score: number) => {
  // 使用渐变色：红色(低分) -> 黄色(中分) -> 绿色(高分)
  if (score < 0.3) {
    return '#ff6b6b' // 红色
  } else if (score < 0.6) {
    return '#feca57' // 黄色
  } else {
    return '#48ca8b' // 绿色
  }
}

onMounted(() => {
  loadExample()
})

watch(
  [schemeCount, criteriaCount],
  () => {
    initializeData()
  },
  { immediate: true },
)
</script>

<style scoped>
.topsis-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header h2 {
  color: #409eff;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
}

.description {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.input-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.input-section h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 20px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.config-row {
  display: flex;
  gap: 30px;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-item label {
  font-weight: 500;
  min-width: 80px;
  color: #333;
}

.criteria-types {
  margin-bottom: 25px;
}

.type-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f5f7fa;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.type-item label {
  font-size: 14px;
  min-width: 60px;
  color: #333;
  font-weight: 500;
}

.matrix-input h4,
.criteria-types h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.matrix-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.matrix-table th,
.matrix-table td {
  padding: 12px;
  text-align: center;
  border: 1px solid #e4e7ed;
}

.matrix-table th {
  background: linear-gradient(135deg, #409eff, #66b3ff);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.matrix-table td:first-child {
  font-weight: 500;
  background: #f8f9fa;
  color: #333;
}

.results-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.results-section h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 20px;
  border-bottom: 2px solid #67c23a;
  padding-bottom: 10px;
}

.result-item {
  margin-bottom: 35px;
}

.result-item h4 {
  color: #409eff;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-item h4::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #409eff, #66b3ff);
  border-radius: 2px;
}

.weights-display {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.weight-item {
  background: linear-gradient(135deg, #e8f4fd, #f0f9ff);
  padding: 15px 20px;
  border-radius: 10px;
  border-left: 4px solid #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  transition: transform 0.2s ease;
}

.weight-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.weight-item span {
  font-weight: 500;
  color: #333;
}

.ranking-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ranking-table th,
.ranking-table td {
  padding: 15px;
  text-align: center;
  border: 1px solid #e4e7ed;
}

.ranking-table th {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.ranking-table tr:nth-child(even) {
  background: #fafbfc;
}

.ranking-table tr:hover {
  background: #e8f5e8;
  transform: scale(1.01);
  transition: all 0.2s ease;
}

.ranking-table td:first-child {
  font-weight: 600;
  color: #409eff;
  font-size: 16px;
}

.chart-container {
  width: 100%;
  height: 600px;
  background: #fafbfc;
  border-radius: 10px;
  border: 2px dashed #e4e7ed;
  margin-top: 15px;
}

.chart-description {
  color: #666;
  font-size: 14px;
  margin: 10px 0;
  font-style: italic;
}

.detail-collapse {
  margin-top: 25px;
}

.detail-section {
  margin-bottom: 30px;
}

.detail-section h5 {
  color: #666;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
}

.detail-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
  background: white;
  border-radius: 6px;
  overflow: hidden;
}

.detail-table th,
.detail-table td {
  padding: 10px;
  text-align: center;
  border: 1px solid #e4e7ed;
}

.detail-table th {
  background: #f5f7fa;
  font-weight: 600;
  color: #333;
  font-size: 12px;
}

.detail-table td:first-child {
  font-weight: 500;
  background: #fafbfc;
  color: #333;
}

.detail-table tr:hover {
  background: #f0f9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .topsis-container {
    padding: 15px;
  }

  .config-row {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .type-row {
    flex-direction: column;
    gap: 10px;
  }

  .weights-display {
    flex-direction: column;
    gap: 10px;
  }

  .chart-container {
    height: 400px;
  }

  .matrix-table,
  .ranking-table,
  .detail-table {
    font-size: 12px;
  }

  .matrix-table th,
  .matrix-table td,
  .ranking-table th,
  .ranking-table td,
  .detail-table th,
  .detail-table td {
    padding: 8px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-item {
  animation: fadeInUp 0.6s ease-out;
}

.result-item:nth-child(2) {
  animation-delay: 0.1s;
}

.result-item:nth-child(3) {
  animation-delay: 0.2s;
}

.result-item:nth-child(4) {
  animation-delay: 0.3s;
}
</style>
