{"name": "baoji", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "translate": "tsx scripts/translate.ts", "i18n:check": "tsx scripts/check-translations.ts"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.0.0", "@vueuse/integrations": "^13.0.0", "axios": "^1.8.4", "clsx": "^2.1.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-plus": "^2.9.7", "flv.js": "^1.6.2", "gl-matrix": "^3.4.3", "http-proxy-agent": "^7.0.2", "lodash": "^4.17.21", "mitt": "^3.0.1", "nprogress": "^0.2.0", "ol": "^10.5.0", "perfect-scrollbar": "^1.5.6", "pinia": "^3.0.1", "quill": "^2.0.3", "quill-image-resize-module": "^3.0.0", "quill-image-resize-vue": "^1.0.4", "tailwind-merge": "^3.1.0", "terraformer-wkt-parser": "^1.2.1", "v-scale-screen": "^2.3.0", "vue": "^3.5.13", "vue-i18n": "^10.0.6", "vue-router": "^4.5.0", "vue3-marquee": "^4.2.2"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.13.9", "@vitalets/google-translate-api": "^9.2.1", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.21.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "sass-embedded": "^1.86.0", "tsx": "^4.7.0", "typescript": "~5.8.0", "unocss": "66.1.0-beta.7", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.1", "vite-plugin-app-loading": "^0.3.1", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}